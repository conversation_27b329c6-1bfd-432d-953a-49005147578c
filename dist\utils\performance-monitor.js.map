{"version": 3, "file": "performance-monitor.js", "sourceRoot": "", "sources": ["../../src/utils/performance-monitor.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAgDrC;;GAEG;AACH,MAAM,OAAO,kBAAkB;IACrB,SAAS,CAAS;IAClB,YAAY,GAAG;QACrB,KAAK,EAAE,CAAC;QACR,UAAU,EAAE,CAAC;QACb,MAAM,EAAE,CAAC;QACT,aAAa,EAAE,EAAc;KAC9B,CAAC;IAEM,cAAc,GAAG;QACvB,iBAAiB,EAAE,CAAC;QACpB,gBAAgB,EAAE,CAAC;QACnB,gBAAgB,EAAE,CAAC;QACnB,YAAY,EAAE,CAAC;KAChB,CAAC;IAEM,YAAY,GAAG;QACrB,cAAc,EAAE,CAAC;QACjB,aAAa,EAAE,CAAC;QAChB,iBAAiB,EAAE,CAAC;QACpB,eAAe,EAAE,CAAC;KACnB,CAAC;IAEF;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,YAAoB,EAAE,OAAgB;QAClD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEnD,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAC7B,CAAC;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAClD,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,4BAA4B;QAC1B,IAAI,CAAC,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,SAA8B;QACnD,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QACrF,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,UAAU;QACR,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAEpC,OAAO;YACL,WAAW,EAAE;gBACX,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,GAAG,EAAE,WAAW,CAAC,GAAG;aACrB;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;aACxB;YACD,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;YACnC,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;gBAC9B,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU;gBACxC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;gBAChC,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,EAAE;aACzD;YACD,cAAc,EAAE,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE;YAC1C,YAAY,EAAE,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE;SACvC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,4BAA4B;QAClC,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACvE,OAAO,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAElC,OAAO;;;;;aAKE,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;aACvD,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACzD,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;WACxD,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;WAElD,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;;YAGjC,OAAO,CAAC,YAAY,CAAC,KAAK;YAC1B,OAAO,CAAC,YAAY,CAAC,UAAU;YAC/B,OAAO,CAAC,YAAY,CAAC,MAAM;cACzB,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;;;YAGrD,OAAO,CAAC,cAAc,CAAC,iBAAiB;YACxC,OAAO,CAAC,cAAc,CAAC,gBAAgB;YACvC,OAAO,CAAC,cAAc,CAAC,gBAAgB;YACvC,OAAO,CAAC,cAAc,CAAC,YAAY;;;YAGnC,OAAO,CAAC,YAAY,CAAC,cAAc;YACnC,OAAO,CAAC,YAAY,CAAC,aAAa;YAClC,OAAO,CAAC,YAAY,CAAC,iBAAiB;YACtC,OAAO,CAAC,YAAY,CAAC,eAAe;CAC/C,CAAC;IACA,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,SAAS;QACT,MAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;QAC9D,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,WAAW,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,SAAS;QACT,IAAI,OAAO,CAAC,YAAY,CAAC,mBAAmB,GAAG,IAAI,EAAE,CAAC;YACpD,QAAQ,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACvF,CAAC;QAED,QAAQ;QACR,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC;YAChD,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG;YAClE,CAAC,CAAC,CAAC,CAAC;QACN,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACpB,QAAQ,CAAC,IAAI,CAAC,YAAY,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACvD,CAAC;QAED,SAAS;QACT,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,aAAa,GAAG,CAAC;YACxD,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,GAAG,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,GAAG,GAAG;YACnF,CAAC,CAAC,CAAC,CAAC;QACN,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;YACrB,QAAQ,CAAC,IAAI,CAAC,YAAY,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,aAAqB,KAAK;QAChD,OAAO,WAAW,CAAC,GAAG,EAAE;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEjD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACjC,CAAC;YAED,YAAY;YACZ,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;gBACpB,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBACjE,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC1C,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK;gBACpC,eAAe,EAAE,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;gBACpE,iBAAiB,EAAE,OAAO,CAAC,cAAc,CAAC,iBAAiB;gBAC3D,cAAc,EAAE,OAAO,CAAC,YAAY,CAAC,cAAc;aACpD,CAAC,CAAC;QACL,CAAC,EAAE,UAAU,CAAC,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG;YAClB,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,CAAC;YACb,MAAM,EAAE,CAAC;YACT,aAAa,EAAE,EAAE;SAClB,CAAC;QACF,IAAI,CAAC,cAAc,GAAG;YACpB,iBAAiB,EAAE,CAAC;YACpB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,CAAC;YACnB,YAAY,EAAE,CAAC;SAChB,CAAC;QACF,IAAI,CAAC,YAAY,GAAG;YAClB,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,CAAC;YAChB,iBAAiB,EAAE,CAAC;YACpB,eAAe,EAAE,CAAC;SACnB,CAAC;IACJ,CAAC;CACF;AAED,WAAW;AACX,MAAM,CAAC,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC"}