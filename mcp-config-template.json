{"mcpServers": {"mcp-feedback-collector": {"command": "node", "args": ["D:/ProgramData/mcp/mcp-feedback-collector-web-main/mcp-feedback-collector-web-main/dist/cli.js"], "env": {"MCP_API_KEY": "************************************************************************************", "MCP_API_BASE_URL": "https://api.x.ai/v1/", "MCP_DEFAULT_MODEL": "grok-3", "MCP_WEB_PORT": "5050", "MCP_DIALOG_TIMEOUT": "60000", "MCP_ENABLE_CHAT": "true", "MCP_CORS_ORIGIN": "*", "MCP_MAX_FILE_SIZE": "10485760", "LOG_LEVEL": "info", "NODE_ENV": "production", "MCP_USE_FIXED_URL": "true", "MCP_FORCE_PORT": "false", "MCP_KILL_PORT_PROCESS": "false", "MCP_CLEANUP_PORT_ON_START": "true"}}}}