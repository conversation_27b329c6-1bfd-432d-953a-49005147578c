{"version": 3, "file": "image-processor.js", "sourceRoot": "", "sources": ["../../src/utils/image-processor.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,OAAO,EAAE,QAAQ,EAAa,MAAM,mBAAmB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAErC;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAE/E;;GAEG;AACH,MAAM,OAAO,cAAc;IACjB,WAAW,CAAS;IACpB,QAAQ,CAAS;IACjB,SAAS,CAAS;IAE1B,YAAY,UAIR,EAAE;QACJ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO;QACnE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAAgB,EAAE,QAAgB;QACpD,UAAU;QACV,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACpD,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,WAAW;QACX,MAAM,cAAc,GAAG;YACrB,YAAY;YACZ,WAAW;YACX,WAAW;YACX,WAAW;YACX,YAAY;YACZ,WAAW;YACX,YAAY;SACb,CAAC;QAEF,OAAO,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAY;QAC5B,OAAO,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QAO7C,IAAI,CAAC;YACH,aAAa;YACb,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;YAC3E,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAEhD,OAAO;gBACL,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,SAAS;gBACpC,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,CAAC;gBAC1B,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC;gBAC5B,IAAI,EAAE,MAAM,CAAC,MAAM;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,KAAK;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,IAAI,QAAQ,CAChB,iCAAiC,EACjC,kBAAkB,EAClB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,UAKpC,EAAE;QACJ,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;YAC3E,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAEpD,MAAM,EACJ,QAAQ,GAAG,IAAI,CAAC,QAAQ,EACxB,SAAS,GAAG,IAAI,CAAC,SAAS,EAC1B,OAAO,GAAG,EAAE,EACZ,MAAM,GAAG,MAAM,EAChB,GAAG,OAAO,CAAC;YAEZ,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAE9B,OAAO;YACP,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC5C,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACtC,IAAI,QAAQ,CAAC,KAAK,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;oBAC7D,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,EAAE;wBAChD,GAAG,EAAE,QAAQ;wBACb,kBAAkB,EAAE,IAAI;qBACzB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,UAAU;YACV,IAAI,YAAoB,CAAC;YACzB,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,MAAM;oBACT,YAAY,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC5D,MAAM;gBACR,KAAK,KAAK;oBACR,YAAY,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;oBACvE,MAAM;gBACR,KAAK,MAAM;oBACT,YAAY,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC5D,MAAM;gBACR;oBACE,YAAY,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;YAChE,CAAC;YAED,YAAY;YACZ,MAAM,gBAAgB,GAAG,cAAc,MAAM,WAAW,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAE1F,MAAM,CAAC,KAAK,CAAC,WAAW,MAAM,CAAC,MAAM,OAAO,YAAY,CAAC,MAAM,QAAQ,CAAC,CAAC;YAEzE,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC/B,MAAM,IAAI,QAAQ,CAChB,0BAA0B,EAC1B,yBAAyB,EACzB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,SAAoB;QAChD,IAAI,CAAC;YACH,SAAS;YACT,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;gBAC1D,MAAM,IAAI,QAAQ,CAChB,6CAA6C,EAC7C,oBAAoB,CACrB,CAAC;YACJ,CAAC;YAED,OAAO;YACP,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9D,MAAM,IAAI,QAAQ,CAChB,6BAA6B,SAAS,CAAC,IAAI,EAAE,EAC7C,oBAAoB,CACrB,CAAC;YACJ,CAAC;YAED,OAAO;YACP,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,QAAQ,CAChB,cAAc,SAAS,CAAC,IAAI,kBAAkB,IAAI,CAAC,WAAW,EAAE,EAChE,iBAAiB,CAClB,CAAC;YACJ,CAAC;YAED,WAAW;YACX,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAE/D,SAAS;YACT,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC/D,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;gBAE7D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,EAAE;oBAC9D,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,OAAO,EAAE,EAAE;iBACZ,CAAC,CAAC;gBAEH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;gBAEzE,OAAO;oBACL,GAAG,SAAS;oBACZ,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,cAAc,CAAC,IAAI;oBACzB,IAAI,EAAE,SAAS,cAAc,CAAC,MAAM,EAAE;iBACvC,CAAC;YACJ,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAClC,MAAM,IAAI,QAAQ,CAChB,sCAAsC,EACtC,wBAAwB,EACxB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAmB;QACrC,MAAM,OAAO,GAAgB,EAAE,CAAC;QAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,CAAC;gBACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;gBACnE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAC;gBACtE,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,MAAM,EAAE,KAAK,CAAC,CAAC;gBACnD,iBAAiB;gBACjB,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC;QAC3D,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAAkB,EAAE,OAAe,GAAG;QAC5D,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;YAC3E,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAEpD,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC;iBACxC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE;gBAClB,GAAG,EAAE,OAAO;gBACZ,QAAQ,EAAE,QAAQ;aACnB,CAAC;iBACD,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;iBACrB,QAAQ,EAAE,CAAC;YAEd,OAAO,0BAA0B,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAChC,MAAM,IAAI,QAAQ,CAChB,8BAA8B,EAC9B,iBAAiB,EACjB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAAmB;QAM/B,MAAM,KAAK,GAAG;YACZ,UAAU,EAAE,MAAM,CAAC,MAAM;YACzB,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,EAA4B;SACtC,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC;YAE9B,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;YACrD,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3D,CAAC;QAED,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAElF,OAAO,KAAK,CAAC;IACf,CAAC;CACF"}