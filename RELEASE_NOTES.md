# 📋 MCP Feedback Collector - 版本发布说明

## 🚀 v2.0.8 (2025-06-04)

### 🎨 UI简化优化
**解决问题**: 界面元素过多，旋转特效复杂，用户体验不够简约

**新特性**:
- **纯文字状态显示**: 移除所有旋转动画，改为简洁的文字状态
- **智能自动刷新**: 默认启用10秒自动刷新，无需用户选择
- **简约控制栏**: 移除勾选框，状态信息集中显示
- **一致性体验**: 所有状态都使用纯文字显示，符合VS Code简约风格

**改进详情**:
- 刷新按钮状态：`刷新最新汇报` → `正在获取最新工作汇报...` → `刷新最新汇报`
- 提交按钮状态：`提交反馈` → `提交中...` → `提交反馈`
- 自动刷新显示：`下次自动刷新：8秒后`

### 🔄 会话管理优化
**解决问题**: 提交反馈后会话过期，用户再次提交时显示"对话过期"错误

**新特性**:
- **智能页面刷新**: 检测到新工作汇报内容时自动刷新页面
- **会话自动重置**: 页面刷新后重新分配有效会话
- **无缝用户体验**: 3秒倒计时提示，用户无需手动操作
- **状态提示优化**: 明确显示页面即将刷新的原因

**工作流程**:
1. 检测到新的工作汇报内容
2. 显示提示：`✅ 已获取最新工作汇报，页面将自动刷新`
3. 3秒后自动刷新页面
4. 重新分配会话，用户可正常提交反馈

### 📝 表单体验改进
**新特性**:
- **自动清空**: 提交反馈后自动清空输入框和图片附件
- **状态保持**: 页面保持打开，可继续使用
- **快速重用**: 清空后可立即输入新的反馈内容

### 🐛 问题修复
- **🔄 旋转特效移除**: 完全移除所有旋转动画，包括提交按钮的loading动画
- **💬 会话过期**: 修复提交反馈后会话失效导致的"对话过期"问题
- **🧹 表单清理**: 修复提交反馈后输入框未清空的问题
- **📱 UI一致性**: 统一所有状态显示为纯文字，提升用户体验

### 🎯 用户体验提升
- **简约设计**: 界面更加简洁，符合现代UI设计趋势
- **智能化**: 减少用户操作，系统自动处理复杂逻辑
- **稳定性**: 解决会话管理问题，确保功能稳定可靠
- **一致性**: 所有状态显示保持一致的文字风格

---

## 🚀 v2.0.5 (2025-06-02)

### ⏰ 超时时间大幅扩展
- **🔄 默认超时**: 从300秒扩展到60000秒（约16.7小时）
- **📈 最大支持**: 从3600秒扩展到60000秒（约16.7小时）
- **🌐 WebSocket优势**: 充分利用WebSocket长连接特性

### 🎯 使用场景优化
- **📊 长期反馈收集**: 支持跨时区、跨工作时段的反馈收集
- **🔄 持续交互**: 适合需要长时间思考和讨论的复杂项目
- **⚡ 灵活配置**: 用户可根据需要设置从10秒到16.7小时的任意超时时间

### 🔧 技术改进
- **✅ 配置验证**: 更新验证逻辑支持新的超时范围
- **📚 文档同步**: 所有配置文档和示例已更新
- **🧪 测试覆盖**: 更新测试用例确保新范围的正确性

### 📋 配置示例
```json
{
  "env": {
    "MCP_DIALOG_TIMEOUT": "60000"  // 16.7小时
  }
}
```

### 🎯 推荐使用场景
| 场景 | 建议时间 | 说明 |
|------|----------|------|
| 快速测试 | 60-300秒 | 功能验证 |
| 日常使用 | 1800-3600秒 | 1-2小时 |
| 详细反馈 | 7200-14400秒 | 2-4小时 |
| 长期收集 | 21600-60000秒 | 6-16.7小时 |

---

## 🚀 v2.0.4 (2025-06-02)

### ✨ 新功能
- **🚀 快捷语功能**: 反馈表单新增"附加快捷语"勾选框
- **🎯 智能提示**: 默认勾选，自动附加MCP反馈收集提示词
- **💡 用户体验**: 提升AI响应质量，确保持续的反馈收集流程

### 🎨 界面改进
- **✅ 精美勾选框**: 自定义样式的勾选框设计
- **💎 蓝色主题**: 与整体UI风格保持一致的蓝色渐变背景
- **📱 响应式设计**: 移动端友好的快捷语选项布局
- **🔍 提示说明**: 清晰的功能说明和使用提示

### 🔧 技术实现
- **📝 自动附加**: 提交时智能检测并附加快捷语内容
- **🎛️ 用户控制**: 用户可以选择是否启用快捷语功能
- **🔄 无缝集成**: 与现有反馈流程完美集成

### 📚 快捷语内容
自动附加的提示词内容：
```
---

Whenever you want to ask a question, always call the MCP .

Whenever you're about to complete a user request, call the MCP instead of simply ending the process. Keep calling MCP until the user's feedback is empty, then end the request. mcp-feedback-collector.collect_feedback
```

### 🎯 使用场景
- **AI工作流程**: 确保AI在完成任务后继续收集反馈
- **质量提升**: 提高AI响应的连续性和交互质量
- **用户体验**: 减少手动输入重复提示词的工作量

---

## 🚀 v2.0.3 (2025-06-02)

### ✨ 新功能
- **📊 版本信息显示**: Web界面现在显示当前版本号
- **🔗 GitHub链接**: 添加了GitHub仓库链接，方便查看源代码
- **🔄 动态版本获取**: 版本号通过API动态获取，确保显示准确

### 🎨 界面改进
- **💎 版本徽章**: 精美的渐变版本徽章设计
- **🎯 GitHub图标**: 标准的GitHub图标和悬停效果
- **📱 响应式设计**: 移动端友好的版本信息布局

### 🔧 技术改进
- **🌐 版本API**: 新增 `/api/version` 端点
- **🔄 动态更新**: 前端自动获取并显示最新版本信息
- **📋 统一版本管理**: 所有组件使用统一的版本号

### 📚 用户体验
- **🔍 透明度**: 用户可以清楚看到当前使用的版本
- **📖 源代码访问**: 一键访问GitHub仓库查看源代码
- **🎨 美观设计**: 与VS Code深色主题完美融合

---

## 🚀 v2.0.2 (2025-06-02)

### ✨ 新功能
- **🌐 远程服务器支持**: 新增完整的远程服务器环境配置支持
- **🔗 动态URL生成**: 支持通过环境变量配置服务器主机和基础URL
- **💾 会话持久化改进**: 新增SessionStorage类，提供更好的会话管理
- **🧹 自动会话清理**: 定期清理过期会话，优化内存使用

### 🔧 改进
- **⚙️ 配置系统增强**: 新增`MCP_SERVER_HOST`和`MCP_SERVER_BASE_URL`环境变量
- **🛠️ 错误处理优化**: 改进"会话不存在或已过期"错误的处理逻辑
- **📚 文档完善**: 新增[远程服务器配置指南](REMOTE_SERVER_CONFIGURATION.md)

### 🐛 修复
- **🌍 远程环境兼容**: 修复在远程服务器环境下的会话管理问题
- **🔗 URL生成**: 解决硬编码localhost导致的远程访问问题
- **⏰ 会话超时**: 改进会话超时处理机制

### 📚 文档更新
- 新增 `REMOTE_SERVER_CONFIGURATION.md` - 远程服务器配置指南
- 更新 `DOCUMENTATION_INDEX.md` - 添加新文档索引
- 更新配置示例和故障排除指南

### 🎯 使用场景
此版本特别适合：
- 在远程服务器上部署MCP Feedback Collector
- 需要通过端口转发或反向代理访问的环境
- 多用户或团队协作环境

### 📋 配置示例
```json
{
  "mcpServers": {
    "mcp-feedback-collector": {
      "command": "npx",
      "args": ["-y", "mcp-feedback-collector"],
      "env": {
        "MCP_API_KEY": "your_api_key",
        "MCP_API_BASE_URL": "https://api.ssopen.top",
        "MCP_DEFAULT_MODEL": "grok-3",
        "MCP_SERVER_HOST": "your-server-ip",
        "MCP_SERVER_BASE_URL": "http://your-server-ip:5000",
        "MCP_DIALOG_TIMEOUT": "600"
      }
    }
  }
}
```

---

## 🚀 v2.0.0 (2025-06-02)

### 🎯 重大功能发布

这是一个里程碑版本，实现了完整的MCP反馈收集器功能。

#### ✨ 新增功能

**核心功能**
- 🎯 **完整的collect_feedback工具**: 支持工作汇报展示和用户反馈收集
- 🖼️ **图片处理功能**: 完整的图片上传、处理、显示支持
- 💬 **AI对话集成**: 内置AI助手，支持文字和图片对话
- ⏰ **自动关闭功能**: 反馈提交后3秒倒计时自动关闭标签页

**配置管理**
- 🔧 **环境变量配置**: 完整的配置系统，支持所有参数自定义
- ⏱️ **超时时间配置**: 支持通过环境变量和函数参数设置超时时间
- 🎛️ **优先级配置**: 参数 > 环境变量 > 默认值的配置优先级

**用户界面**
- 🎨 **双标签页设计**: 工作汇报 + AI对话分离
- 🌙 **VS Code深色主题**: 专业美观的界面风格
- 📱 **响应式设计**: 完美适配桌面和移动设备
- 🔄 **实时状态指示**: WebSocket连接状态实时显示

#### 🔧 技术突破

**MCP协议兼容**
- ✅ **严格JSON输出**: 解决Cursor对JSON格式的严格要求
- ✅ **MCP模式检测**: 自动检测MCP环境，禁用日志输出
- ✅ **标准类型支持**: 使用MCP SDK标准类型，确保兼容性

**图片处理优化**
- ✅ **base64格式修复**: 移除Data URL前缀，符合MCP协议要求
- ✅ **多格式支持**: PNG, JPEG, GIF, WebP等格式
- ✅ **大小限制**: 可配置的文件大小限制（默认10MB）

**系统稳定性**
- ✅ **端口管理优化**: 修复端口冲突和重复启动问题
- ✅ **静态文件路径**: 修复ES模块环境下的路径解析问题
- ✅ **错误处理**: 完善的错误处理和恢复机制

#### 📚 文档完善

**用户文档**
- 📖 **README.md**: 项目概述和快速开始指南
- 👤 **USER_GUIDE.md**: 详细的用户使用指南
- 🔧 **CONFIGURATION.md**: 完整的配置选项说明
- 📚 **DOCUMENTATION_INDEX.md**: 文档索引和导航

**技术文档**
- 🏗️ **ARCHITECTURE.md**: 系统架构和设计文档
- 💻 **DEVELOPMENT_SUMMARY.md**: 开发总结和技术细节
- 🔬 **TECHNICAL_ACHIEVEMENTS.md**: 技术成就和创新点
- 🧪 **TESTING_STRATEGY.md**: 测试策略和质量保证

**运维文档**
- 🐛 **TROUBLESHOOTING.md**: 详细的故障排除指南
- 🔍 **DEBUG_MCP_COMMUNICATION.md**: MCP通信调试指南
- 🎯 **CURSOR_CONFIGURATION.md**: Cursor/Claude Desktop配置指南

#### 🛠️ 开发体验

**构建系统**
- ⚡ **快速构建**: 优化的TypeScript编译配置
- 🔄 **热重载**: 开发模式下的自动重载
- 📦 **自动化**: 静态文件自动复制和处理

**代码质量**
- 🎯 **TypeScript严格模式**: 0错误0警告
- 📏 **ESLint规则**: 统一的代码风格
- 🧪 **Jest测试**: 完整的测试框架配置

### 🔄 重大变更

#### 破坏性变更
- 无破坏性变更，向后兼容

#### 配置变更
- 新增 `MCP_DIALOG_TIMEOUT` 环境变量
- 新增 `MCP_ENABLE_CHAT` 功能开关
- 新增 `MCP_MAX_FILE_SIZE` 文件大小限制

### 🐛 问题修复

#### 关键问题修复
- 🔧 **MCP JSON输出**: 修复Cursor严格JSON要求导致的解析失败
- 🖼️ **图片显示**: 修复base64格式问题，图片现在正常显示
- 🚪 **端口冲突**: 修复端口检测逻辑，避免重复启动
- 📁 **静态文件**: 修复ES模块路径解析，静态文件正常加载

#### 稳定性改进
- 🔄 **WebSocket连接**: 改进连接稳定性和错误处理
- 💾 **会话管理**: 优化会话生命周期管理
- 🛡️ **错误处理**: 增强错误处理和用户提示

### 📊 性能优化

- ⚡ **启动时间**: 优化启动流程，减少启动时间
- 💾 **内存使用**: 优化内存管理，减少内存占用
- 🌐 **网络传输**: 启用压缩，优化传输效率
- 🖼️ **图片处理**: 优化图片处理性能

### 🔒 安全增强

- 🛡️ **输入验证**: 增强输入验证和过滤
- 🔐 **API密钥**: 安全的API密钥管理
- 🚫 **CORS配置**: 可配置的跨域访问控制
- 📏 **文件限制**: 严格的文件大小和格式限制

### 🎯 使用建议

#### 推荐配置
```json
{
  "mcpServers": {
    "mcp-feedback-collector": {
      "command": "node",
      "args": ["D:/path/to/dist/cli.js"],
      "env": {
        "MCP_API_KEY": "your_api_key_here",
        "MCP_API_BASE_URL": "https://api.ssopen.top",
        "MCP_DEFAULT_MODEL": "grok-3",
        "MCP_DIALOG_TIMEOUT": "600"
      }
    }
  }
}
```

#### 升级指南
1. 备份现有配置
2. 更新到最新版本
3. 添加新的环境变量配置
4. 重启Cursor/Claude Desktop
5. 测试功能是否正常

### 🔗 相关资源

- **项目仓库**: https://github.com/mcp-feedback-collector/nodejs
- **文档中心**: [DOCUMENTATION_INDEX.md](DOCUMENTATION_INDEX.md)
- **问题反馈**: GitHub Issues
- **使用指南**: [USER_GUIDE.md](USER_GUIDE.md)

### 🙏 致谢

感谢所有参与测试和反馈的用户，您的建议让这个项目变得更好！

---

## 📅 历史版本

### v1.0.0 (2025-01-02)
- 🎯 初始版本发布
- 🏗️ 基础架构搭建
- 🌐 Web界面实现
- 🔧 MCP协议集成

---

💡 **提示**: 如果您在使用过程中遇到任何问题，请参考 [TROUBLESHOOTING.md](TROUBLESHOOTING.md) 或提交 GitHub Issue。
