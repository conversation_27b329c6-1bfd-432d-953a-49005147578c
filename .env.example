# MCP Feedback Collector 配置文件

# AI API配置
MCP_API_KEY="************************************************************************************"
MCP_API_BASE_URL="https://api.x.ai/v1/"  # 推荐使用这个中转站
MCP_DEFAULT_MODEL="grok-3"

# Web服务器配置
MCP_WEB_PORT="5000"
MCP_DIALOG_TIMEOUT="60000"  # 反馈收集超时时间（秒），范围：10-60000

# 功能开关
MCP_ENABLE_CHAT="true"

# 开发模式配置
NODE_ENV="production"
DEBUG="false"

# 安全配置
MCP_CORS_ORIGIN="*"
MCP_MAX_FILE_SIZE="10485760"  # 10MB in bytes

# 日志配置
LOG_LEVEL="info"
