{"version": 3, "file": "port-manager.js", "sourceRoot": "", "sources": ["../../src/utils/port-manager.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,KAAK,CAAC;AACnC,OAAO,EAAE,QAAQ,EAAY,MAAM,mBAAmB,CAAC;AACvD,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAEtD;;GAEG;AACH,MAAM,OAAO,WAAW;IACL,gBAAgB,GAAG,IAAI,CAAC;IACxB,cAAc,GAAG,IAAI,CAAC;IACtB,WAAW,GAAG,EAAE,CAAC;IAElC;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,IAAY;QAChC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC;YAC9B,IAAI,QAAQ,GAAG,KAAK,CAAC;YAErB,eAAe;YACf,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,QAAQ,GAAG,IAAI,CAAC;oBAChB,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;wBAChB,OAAO,CAAC,KAAK,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;gBACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,QAAQ,GAAG,IAAI,CAAC;oBAChB,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,iBAAiB;oBACjB,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;wBAChB,OAAO,CAAC,IAAI,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAQ,EAAE,EAAE;gBAC9B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,QAAQ,GAAG,IAAI,CAAC;oBAChB,YAAY,CAAC,OAAO,CAAC,CAAC;oBACtB,QAAQ;oBACR,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,IAAY;QACrC,WAAW;QACX,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,eAAe;QACf,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC9D,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,SAAS,EAAE,WAAW,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,aAAsB;QAC5C,mBAAmB;QACnB,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,WAAW,aAAa,EAAE,CAAC,CAAC;YACzC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAC5D,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,CAAC,IAAI,CAAC,WAAW,aAAa,EAAE,CAAC,CAAC;gBACxC,OAAO,aAAa,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,QAAQ,aAAa,gBAAgB,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,eAAe;QACf,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE,CAAC;YAC3E,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;YAC9B,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;gBAC/B,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,WAAW,UAAU,EAAE,CAAC,CAAC;YACtC,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC3C,MAAM,CAAC,IAAI,CAAC,aAAa,UAAU,EAAE,CAAC,CAAC;gBACvC,OAAO,UAAU,CAAC;YACpB,CAAC;QACH,CAAC;QAED,MAAM,IAAI,QAAQ,CAChB,0BAA0B,EAC1B,oBAAoB,EACpB;YACE,aAAa;YACb,UAAU,EAAE,IAAI,CAAC,gBAAgB;YACjC,QAAQ,EAAE,IAAI,CAAC,cAAc;YAC7B,UAAU,EAAE,IAAI,CAAC,WAAW;SAC7B,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,IAAY;QAC5B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnD,OAAO;YACL,IAAI;YACJ,SAAS;YACT,yBAAyB;YACzB,GAAG,EAAE,SAAS;SACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,MAAM,OAAO,GAAe,EAAE,CAAC;QAE/B,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE,CAAC;YAC3E,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB;QAC1B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE3B,IAAI,CAAC;YACH,mBAAmB;YACnB,8BAA8B;YAC9B,uBAAuB;YAEvB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,IAAY,EAAE,cAAuB,KAAK;QACxD,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QAE/B,WAAW;QACX,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,QAAQ,CAChB,QAAQ,IAAI,0CAA0C,EACtD,eAAe,EACf,EAAE,IAAI,EAAE,WAAW,EAAE,CACtB,CAAC;QACJ,CAAC;QAED,WAAW;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,gBAAgB,CAAC,CAAC;QACxC,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAE7D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,QAAQ,CAChB,gCAAgC,IAAI,EAAE,EACtC,2BAA2B,EAC3B,EAAE,IAAI,EAAE,CACT,CAAC;QACJ,CAAC;QAED,aAAa;QACb,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,QAAQ,CAChB,QAAQ,IAAI,wCAAwC,EACpD,qBAAqB,EACrB,EAAE,IAAI,EAAE,CACT,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAAE,YAAoB,KAAK;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,aAAa,SAAS,IAAI,CAAC,CAAC;QAEpD,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,SAAS,EAAE,CAAC;YAC1C,iBAAiB;YACjB,IAAI,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,qBAAqB;YACrB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,IAAI,QAAQ,CAChB,QAAQ,IAAI,4BAA4B,SAAS,IAAI,EACrD,sBAAsB,EACtB,EAAE,IAAI,EAAE,SAAS,EAAE,CACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,IAAY;QAC5B,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QAE/B,YAAY;QACZ,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,YAAY,CAAC,CAAC;YACpC,OAAO;QACT,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,OAAO,EAAE;YACjC,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,OAAO,EAAE,WAAW,CAAC,OAAO;SAC7B,CAAC,CAAC;QAEH,aAAa;QACb,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,oBAAoB,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9D,OAAO;QACT,CAAC;QAED,SAAS;QACT,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,SAAS,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QACxD,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAExE,IAAI,MAAM,EAAE,CAAC;YACX,SAAS;YACT,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC;YACjC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,mBAAmB,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,SAAS,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,IAAY;QACjC,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QAE/B,IAAI,CAAC;YACH,mBAAmB;YACnB,kBAAkB;YAClB,WAAW;YACX,YAAY;YAEZ,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC;QAEnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,MAAM,EAAE,KAAK,CAAC,CAAC;YAC1C,MAAM,IAAI,QAAQ,CAChB,gCAAgC,IAAI,EAAE,EACtC,sBAAsB,EACtB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;CACF"}