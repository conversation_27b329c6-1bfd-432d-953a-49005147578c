{"version": 3, "file": "mcp-server.js", "sourceRoot": "", "sources": ["../../src/server/mcp-server.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,yCAAyC,CAAC;AACpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AAEjF,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAiC,QAAQ,EAA2B,MAAM,mBAAmB,CAAC;AACrG,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C;;GAEG;AACH,MAAM,OAAO,SAAS;IACZ,SAAS,CAAY;IACrB,SAAS,CAAY;IACrB,MAAM,CAAS;IACf,SAAS,GAAG,KAAK,CAAC;IAE1B,YAAY,MAAc;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,aAAa;QACb,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC;YAC7B,IAAI,EAAE,wBAAwB;YAC9B,OAAO,EAAE,OAAO;SACjB,EAAE;YACD,YAAY,EAAE;gBACZ,KAAK,EAAE,EAAE;aACV;SACF,CAAC,CAAC;QAEH,YAAY;QACZ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5B,CAAC,CAAC;QAEF,aAAa;QACb,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;QAEvC,YAAY;QACZ,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,4CAA4C;QAC5C,IAAI,CAAC,SAAS,CAAC,YAAY,CACzB,kBAAkB,EAClB;YACE,WAAW,EAAE,qIAAqI;YAClJ,WAAW,EAAE;gBACX,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CAAC;aAC3D;SACF,EACD,KAAK,EAAE,IAA8B,EAA2B,EAAE;YAChE,MAAM,MAAM,GAA0B;gBACpC,YAAY,EAAE,IAAI,CAAC,YAAY;aAChC,CAAC;YAEF,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAEvC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAClD,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC/C,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;gBAE/C,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;oBAC9B,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,MAAM,IAAI,QAAQ,CAChB,4BAA4B,EAC5B,wBAAwB,EACxB,KAAK,CACN,CAAC;YACJ,CAAC;QACH,CAAC,CACF,CAAC;QAEF,IAAI,MAAM,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,MAA6B;QACzD,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC;QAChC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAElD,MAAM,CAAC,IAAI,CAAC,kBAAkB,YAAY,CAAC,MAAM,UAAU,eAAe,GAAG,CAAC,CAAC;QAE/E,IAAI,CAAC;YACH,kBAAkB;YAClB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC;YAED,SAAS;YACT,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;YAErF,MAAM,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,MAAM,CAAC,CAAC;YAEhD,sBAAsB;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAEpD,OAAO;gBACL,OAAO;gBACP,OAAO,EAAE,KAAK;aACf,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAE/B,MAAM,YAAY,GAAG,KAAK,YAAY,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,iCAAiC,CAAC;YAEnG,OAAO;gBACL,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,OAAO,YAAY,EAAE;qBAC5B,CAAC;gBACF,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAwB;QACnD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC;oBACN,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;iBAChB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAmC,EAAE,CAAC;QAEnD,SAAS;QACT,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM,QAAQ,CAAC,MAAM,WAAW;SACvC,CAAC,CAAC;QAEH,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/B,SAAS;YACT,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,YAAY,KAAK,GAAG,CAAC,MAAM;aAClC,CAAC,CAAC;YAEH,SAAS;YACT,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE;iBAC3B,CAAC,CAAC;YACL,CAAC;YAED,oBAAoB;YACpB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;iBACpC,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAc,EAAE,QAAgB,EAAE,EAAE;oBACvD,SAAS;oBACT,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;qBACzF,CAAC,CAAC;oBAEH,mBAAmB;oBACnB,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;wBACb,6CAA6C;wBAC7C,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;wBAEtE,OAAO,CAAC,IAAI,CAAC;4BACX,IAAI,EAAE,OAAO;4BACb,IAAI,EAAE,UAAU,EAAE,eAAe;4BACjC,QAAQ,EAAE,GAAG,CAAC,IAAI;yBACnB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,QAAQ;YACR,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,IAAI;aAC7D,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAwB;QACnD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,WAAW,CAAC,CAAC;QAE7C,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/B,KAAK,CAAC,IAAI,CAAC,UAAU,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC;YAEtC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACnC,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAc,EAAE,QAAgB,EAAE,EAAE;oBACvD,KAAK,CAAC,IAAI,CAAC,QAAQ,QAAQ,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBACnG,CAAC,CAAC,CAAC;YACL,CAAC;YAED,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YACjE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAE7B,WAAW;YACX,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAE7B,UAAU;YACV,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;YAE7C,WAAW;YACX,SAAS,CAAC,OAAO,GAAG,CAAC,KAAY,EAAE,EAAE;gBACnC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAClC,CAAC,CAAC;YAEF,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;gBACvB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,CAAC,CAAC;YAEF,SAAS;YACT,MAAM,iBAAiB,GAAG,SAAS,CAAC,SAAS,CAAC;YAC9C,SAAS,CAAC,SAAS,GAAG,CAAC,OAAO,EAAE,EAAE;gBAChC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC9D,IAAI,iBAAiB,EAAE,CAAC;oBACtB,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC;YAEF,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpD,SAAS,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE;gBAC3B,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC9D,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC;YAC/B,CAAC,CAAC;YAEF,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAExC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACnC,MAAM,IAAI,QAAQ,CAChB,4BAA4B,EAC5B,oBAAoB,EACpB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE5B,YAAY;YACZ,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAE7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE5B,SAAS;YACT,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACnC,MAAM,IAAI,QAAQ,CAChB,4BAA4B,EAC5B,wBAAwB,EACxB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1B,WAAW;YACX,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YAE5B,WAAW;YACX,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC;YAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,IAAI,QAAQ,CAChB,uBAAuB,EACvB,mBAAmB,EACnB,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;SAC3E,CAAC;IACJ,CAAC;CACF"}