# 🔧 MCP Feedback Collector 配置指南

## 📋 配置概述

本项目支持多种MCP配置方式，适用于不同的使用场景：

1. **NPM包运行**（推荐）- 使用已发布的npm包
2. **本地源码运行** - 直接运行本地构建的代码
3. **开发模式运行** - 直接运行TypeScript源码

## 🚀 方式一：NPM包运行（推荐）

### Claude Desktop 配置

在Claude Desktop的MCP配置文件中添加：

```json
{
  "mcpServers": {
    "mcp-feedback-collector": {
      "command": "npx",
      "args": ["-y", "mcp-feedback-collector@latest"],
      "env": {
        "MCP_API_KEY": "your_api_key_here",
        "MCP_API_BASE_URL": "https://api.ssopen.top",
        "MCP_DEFAULT_MODEL": "grok-3",
        "MCP_WEB_PORT": "5050",
        "MCP_DIALOG_TIMEOUT": "60000"
      }
    }
  }
}
```

### Cursor 配置

在Cursor的MCP设置中添加相同配置：

```json
{
  "mcpServers": {
    "mcp-feedback-collector": {
      "command": "npx",
      "args": ["-y", "mcp-feedback-collector@latest"],
      "env": {
        "MCP_API_KEY": "************************************************************************************",
        "MCP_API_BASE_URL": "https://api.x.ai/v1/",
        "MCP_DEFAULT_MODEL": "grok-3",
        "MCP_WEB_PORT": "5050",
        "MCP_DIALOG_TIMEOUT": "60000"
      }
    }
  }
}
```

## 🛠️ 方式二：本地源码运行

### 前提条件
- 已完成项目构建：`npm run build`
- dist目录存在且包含编译后的文件

### 配置示例

```json
{
  "mcpServers": {
    "mcp-feedback-collector": {
      "command": "node",
      "args": ["D:/ProgramData/mcp/mcp-feedback-collector-web-main/mcp-feedback-collector-web-main/dist/cli.js"],
      "env": {
        "MCP_API_KEY": "************************************************************************************",
        "MCP_API_BASE_URL": "https://api.x.ai/v1/",
        "MCP_DEFAULT_MODEL": "grok-3",
        "MCP_WEB_PORT": "5050",
        "MCP_DIALOG_TIMEOUT": "60000"
      }
    }
  }
}
```

## 🔧 方式三：开发模式运行

### 使用tsx直接运行TypeScript源码

```json
{
  "mcpServers": {
    "mcp-feedback-collector": {
      "command": "npx",
      "args": ["tsx", "D:/ProgramData/mcp/mcp-feedback-collector-web-main/mcp-feedback-collector-web-main/src/cli.ts"],
      "env": {
        "MCP_API_KEY": "************************************************************************************",
        "MCP_API_BASE_URL": "https://api.x.ai/v1/",
        "MCP_DEFAULT_MODEL": "grok-3",
        "MCP_WEB_PORT": "5050",
        "MCP_DIALOG_TIMEOUT": "60000",
        "NODE_ENV": "development"
      }
    }
  }
}
```

## 📝 环境变量说明

| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `MCP_API_KEY` | AI API密钥 | 无 | `xai-xxx...` |
| `MCP_API_BASE_URL` | API基础URL | `https://api.ssopen.top` | `https://api.x.ai/v1/` |
| `MCP_DEFAULT_MODEL` | 默认AI模型 | `gpt-4o-mini` | `grok-3` |
| `MCP_WEB_PORT` | Web服务端口 | `5000` | `5050` |
| `MCP_DIALOG_TIMEOUT` | 对话超时时间(秒) | `60000` | `60000` |
| `MCP_ENABLE_CHAT` | 启用聊天功能 | `true` | `true` |
| `MCP_CORS_ORIGIN` | CORS源设置 | `*` | `*` |
| `MCP_MAX_FILE_SIZE` | 最大文件大小 | `10485760` | `10485760` |
| `LOG_LEVEL` | 日志级别 | `info` | `debug` |

## ⚙️ 高级配置选项

### URL和端口优化配置

```json
"env": {
  "MCP_USE_FIXED_URL": "true",
  "MCP_FORCE_PORT": "false",
  "MCP_KILL_PORT_PROCESS": "false",
  "MCP_CLEANUP_PORT_ON_START": "true"
}
```

### 服务器配置

```json
"env": {
  "MCP_SERVER_HOST": "localhost",
  "MCP_SERVER_BASE_URL": "http://localhost:5050"
}
```

## 🧪 配置测试与验证

### 1. 测试配置有效性

```bash
# 检查配置
node dist/cli.js config

# 健康检查
node dist/cli.js health

# 测试反馈功能
node dist/cli.js test-feedback
```

### 2. 验证MCP连接

在Claude Desktop或Cursor中：
1. 重启应用
2. 检查MCP服务器状态（应显示绿色）
3. 尝试调用 `collect_feedback` 工具

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -an | findstr :5050

   # 更换端口
   "MCP_WEB_PORT": "5051"
   ```

2. **API密钥错误**
   - 确保API密钥正确
   - 检查API基础URL是否可访问

3. **路径问题**
   - 使用绝对路径
   - 确保文件存在且可执行

### 调试模式

启用详细日志：
```json
"env": {
  "LOG_LEVEL": "debug",
  "DEBUG": "true"
}
```

## 📊 性能监控

### 获取性能指标

```bash
# 查看性能报告
node dist/cli.js metrics

# JSON格式输出
node dist/cli.js metrics --format json
```

## 🔐 安全配置

### 生产环境建议

```json
"env": {
  "NODE_ENV": "production",
  "MCP_CORS_ORIGIN": "https://your-domain.com",
  "LOG_LEVEL": "warn"
}
```

## 🚀 快速启动脚本

创建启动脚本 `start-mcp.bat`：

```batch
@echo off
echo 启动 MCP Feedback Collector...
cd /d "D:\ProgramData\mcp\mcp-feedback-collector-web-main\mcp-feedback-collector-web-main"
node dist/cli.js
pause
```

## 📱 Cursor规则配置

在Cursor的规则中添加：

```
"Whenever you want to ask a question, always call the MCP.

Whenever you're about to complete a user request, call the MCP instead of simply ending the process. Keep calling MCP until the user's feedback is empty, then end the request. mcp-feedback-collector.collect_feedback"
```

## ✅ 配置检查清单

- [ ] API密钥已配置
- [ ] 端口未被占用
- [ ] 路径正确（如使用本地运行）
- [ ] 项目已构建（如使用本地运行）
- [ ] MCP服务器状态为绿色
- [ ] 可以成功调用工具函数

## 🎯 推荐配置

基于当前项目状态，推荐使用**本地源码运行**方式：

```json
{
  "mcpServers": {
    "mcp-feedback-collector": {
      "command": "node",
      "args": ["D:/ProgramData/mcp/mcp-feedback-collector-web-main/mcp-feedback-collector-web-main/dist/cli.js"],
      "env": {
        "MCP_API_KEY": "************************************************************************************",
        "MCP_API_BASE_URL": "https://api.x.ai/v1/",
        "MCP_DEFAULT_MODEL": "grok-3",
        "MCP_WEB_PORT": "5050",
        "MCP_DIALOG_TIMEOUT": "60000",
        "MCP_ENABLE_CHAT": "true",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```
