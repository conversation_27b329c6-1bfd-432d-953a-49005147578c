<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 MCP Feedback Collector - Web版</title>
    <style>
        /* VS Code 风格的深色主题 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Segoe UI", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
            background: #1e1e1e;
            color: #cccccc;
            font-size: 13px;
            line-height: 1.5;
            padding: 20px;
        }

        /* 主容器 */
        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        /* 页面标题 */
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .page-subtitle {
            font-size: 14px;
            color: #969696;
            margin-bottom: 24px;
        }

        /* 连接状态 */
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .connection-status.connected {
            background: #4ec9b0;
            color: #1e1e1e;
        }

        .connection-status.disconnected {
            background: #f14c4c;
            color: white;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            margin-bottom: 24px;
            border-bottom: 1px solid #3e3e42;
        }

        .nav-tab {
            padding: 12px 20px;
            background: transparent;
            border: none;
            color: #969696;
            font-size: 13px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .nav-tab:hover {
            color: #cccccc;
        }

        .nav-tab.active {
            color: #ffffff;
            border-bottom-color: #007acc;
        }

        /* 获取key链接 */
        .get-key-link {
            padding: 12px 20px;
            color: #007acc;
            text-decoration: none;
            font-size: 13px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .get-key-link:hover {
            color: #ffffff;
            text-decoration: none;
        }

        /* 内容区域 */
        .content-area {
            display: none;
        }

        .content-area.active {
            display: block;
        }

        /* 工作汇报控制栏 */
        .report-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #252526;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            margin-bottom: 16px;
        }

        .refresh-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: background-color 0.2s;
        }

        .refresh-btn:hover {
            background: #005a9e;
        }

        .refresh-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        /* 状态文字显示 */
        .refresh-status-text {
            font-size: 12px;
            color: #888;
            margin-left: 16px;
            flex: 1;
            text-align: right;
        }

        .refresh-status-text.loading {
            color: #3b82f6;
        }

        .refresh-status-text.success {
            color: #22c55e;
        }

        .refresh-status-text.error {
            color: #ef4444;
        }

        /* 工作汇报卡片 */
        .report-card {
            background: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            margin-bottom: 16px;
            overflow: hidden;
        }

        .card-header {
            padding: 16px 20px;
            background: #323233;
            border-bottom: 1px solid #3e3e42;
            color: #ffffff;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-subtitle {
            font-size: 12px;
            color: #969696;
        }

        .card-body {
            padding: 16px 20px;
        }

        /* 反馈表单卡片 */
        .feedback-card {
            background: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            margin-top: 24px;
            overflow: hidden;
        }

        .feedback-header {
            padding: 16px 20px;
            background: #323233;
            border-bottom: 1px solid #3e3e42;
            color: #ffffff;
        }

        .feedback-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feedback-body {
            padding: 20px;
        }

        /* 表单元素 */
        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 12px;
            color: #969696;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            background: #1e1e1e;
            color: #cccccc;
            font-family: inherit;
            font-size: 13px;
            resize: vertical;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #007acc;
        }

        .form-textarea::placeholder {
            color: #6a6a6a;
        }

        /* 图片预览区域 */
        .image-preview-area {
            margin-bottom: 16px;
        }

        .image-previews {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 8px;
        }

        .image-preview {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 4px;
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            overflow: hidden;
        }

        .preview-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .remove-btn {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #e81123;
            color: white;
            border: none;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 工具栏 */
        .toolbar {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }

        .toolbar-btn {
            padding: 6px 12px;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            background: #2d2d30;
            color: #cccccc;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .toolbar-btn:hover {
            background: #3c3c3c;
            border-color: #007acc;
        }

        /* 按钮组 */
        .button-group {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            border: none;
        }

        .btn-secondary {
            background: transparent;
            color: #969696;
            border: 1px solid #3e3e42;
        }

        .btn-secondary:hover {
            background: #3c3c3c;
        }

        .btn-primary {
            background: #007acc;
            color: white;
            border: 1px solid #007acc;
        }

        .btn-primary:hover {
            background: #005a9e;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 状态消息 */
        .status-message {
            padding: 12px;
            border-radius: 4px;
            margin: 16px 0;
            font-size: 13px;
        }

        .status-message.success {
            background: #4ec9b0;
            color: #1e1e1e;
        }

        .status-message.error {
            background: #f14c4c;
            color: white;
        }

        .status-message.info {
            background: #007acc;
            color: white;
        }

        /* 加载状态 - 简化为纯文字显示 */
        .loading {
            display: inline;
            color: #007acc;
            font-weight: 500;
        }

        /* 聊天界面样式 */
        .chat-container {
            background: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            height: 500px;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 16px 20px;
            background: #323233;
            border-bottom: 1px solid #3e3e42;
            color: #ffffff;
        }

        .chat-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .message {
            display: flex;
            margin-bottom: 16px;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-bubble {
            max-width: 70%;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 13px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message.user .message-bubble {
            background: #007acc;
            color: white;
        }

        .message.ai .message-bubble {
            background: #323233;
            color: #cccccc;
            border: 1px solid #3e3e42;
        }

        .message-time {
            text-align: center;
            color: #969696;
            font-size: 12px;
            margin: 16px 0 8px;
        }

        .api-hint {
            background: #323233;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: center;
        }

        .api-hint-title {
            color: #4ec9b0;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .api-hint-link {
            color: #007acc;
            text-decoration: none;
        }

        .api-hint-desc {
            font-size: 12px;
            color: #969696;
            margin-top: 4px;
        }

        .chat-input-area {
            background: #323233;
            border-top: 1px solid #3e3e42;
            padding: 12px;
        }

        .chat-input-row {
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .chat-input {
            flex: 1;
            min-height: 32px;
            max-height: 80px;
            padding: 6px 12px;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            background: #1e1e1e;
            color: #cccccc;
            resize: none;
            font-family: inherit;
            font-size: 13px;
            line-height: 1.4;
        }

        .chat-input:focus {
            outline: none;
            border-color: #007acc;
        }

        .chat-actions {
            display: flex;
            gap: 4px;
        }

        .chat-btn {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            border: 1px solid #3e3e42;
            background: #2d2d30;
            color: #cccccc;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .chat-btn:hover {
            background: #3c3c3c;
            border-color: #007acc;
        }

        .send-btn {
            background: #007acc !important;
            border-color: #007acc !important;
            color: white !important;
        }

        .send-btn:hover {
            background: #005a9e !important;
        }

        .image-previews-container {
            margin-bottom: 8px;
            padding: 8px;
            background: #1e1e1e;
            border-radius: 4px;
            border: 1px solid #3e3e42;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 14px;
        }

        ::-webkit-scrollbar-track {
            background: #1e1e1e;
        }

        ::-webkit-scrollbar-thumb {
            background: #424242;
            border-radius: 7px;
            border: 3px solid #1e1e1e;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #4f4f4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 连接状态 -->
        <div id="connection-status" class="connection-status disconnected">
            连接中...
        </div>

        <!-- 页面标题 -->
        <h1 class="page-title">
            <span>🎯</span>
            MCP Feedback Collector - Web版
        </h1>
        <p class="page-subtitle">基于Node.js + WebSocket的现代化反馈收集器</p>

        <!-- 版本信息和链接 -->
        <div class="version-info">
            <span class="version-badge">v<span id="version-number">2.0.8</span></span>
            <a href="https://github.com/sanshao85/mcp-feedback-collector-web" target="_blank" class="github-link" title="查看源代码">
                <svg class="github-icon" viewBox="0 0 24 24" width="20" height="20">
                    <path fill="currentColor" d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                GitHub
            </a>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('report')">📋 工作汇报</button>
            <button class="nav-tab" onclick="switchTab('chat')">💬 AI对话</button>
            <a href="https://api.ssopen.top" target="_blank" class="get-key-link">🔑 获取key</a>
        </div>

        <!-- 状态消息区域 -->
        <div id="status-messages"></div>

        <!-- 工作汇报内容区域 -->
        <div id="report-content" class="content-area active">
            <!-- 工作汇报控制栏 -->
            <div class="report-controls">
                <button id="refresh-report-btn" class="refresh-btn" onclick="refreshWorkSummary()" title="刷新最新工作汇报">
                    <span id="refresh-text">刷新最新汇报</span>
                </button>
                <!-- 状态文字显示 -->
                <div id="refresh-status-text" class="refresh-status-text">下次自动刷新：<span id="auto-refresh-countdown">10</span>秒后</div>
            </div>

            <!-- 动态工作汇报内容将在这里显示 -->
            <div id="default-message" class="report-card" style="text-align: center; padding: 40px;">
                <div class="card-body">
                    <div style="color: #969696; font-size: 14px;">
                        <span style="font-size: 24px;">📋</span>
                        <br><br>
                        等待AI工作汇报...
                        <br><br>
                        <small>当AI调用 collect_feedback() 时，工作汇报内容将显示在这里</small>
                        <br><br>
                        <button onclick="refreshWorkSummary()" style="background: #007acc; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            🔄 手动刷新
                        </button>
                    </div>
                </div>
            </div>

            <!-- 反馈表单 -->
            <div class="feedback-card">
                <div class="feedback-header">
                    <div class="feedback-title">
                        <span>💬</span>
                        您的反馈
                    </div>
                </div>
                <div class="feedback-body">
                    <form id="feedback-form">
                        <div class="form-group">
                            <label class="form-label">反馈内容</label>
                            <textarea
                                id="feedback-text"
                                class="form-textarea"
                                placeholder="请输入您对本次工作的反馈和建议..."
                            ></textarea>

                            <!-- 快捷语选项 -->
                            <div class="quick-phrase-option">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="add-quick-phrase" checked>
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">附加快捷语（推荐）</span>
                                </label>
                                <div class="quick-phrase-hint">
                                    自动附加MCP反馈收集提示词，提升AI响应质量
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">附件图片（可选）</label>
                            <div class="toolbar">
                                <button type="button" class="toolbar-btn" onclick="selectImages()">
                                    📁 选择图片
                                </button>
                                <button type="button" class="toolbar-btn" onclick="pasteImages()">
                                    📋 粘贴图片
                                </button>
                            </div>

                            <div class="image-preview-area">
                                <div class="image-previews" id="image-previews"></div>
                            </div>
                        </div>

                        <div class="button-group">
                            <button type="button" class="btn btn-secondary" onclick="clearFeedbackForm()">
                                清空
                            </button>
                            <button type="submit" class="btn btn-primary" id="submit-feedback-btn">
                                提交反馈
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- AI对话内容区域 -->
        <div id="chat-content" class="content-area">
            <div class="chat-container">
                <div class="chat-header">
                    <div class="chat-title">
                        <span>💬</span>
                        AI 助手对话
                    </div>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <div class="message-time">今天</div>

                    <div class="message ai">
                        <div class="message-bubble">您好！我是AI助手，有什么可以帮助您的吗？</div>
                    </div>

                    <div id="api-hint" class="api-hint" style="display: none;">
                        <div class="api-hint-title">🔑 需要API密钥</div>
                        <div>
                            <a href="https://api.ssopen.top/" target="_blank" class="api-hint-link">
                                点击获取 SSOpen API 密钥
                            </a>
                        </div>
                        <div class="api-hint-desc">高速稳定 | 价格优惠 | 安全可靠</div>
                    </div>
                </div>

                <div class="chat-input-area">
                    <!-- 聊天图片预览区域 -->
                    <div id="chat-image-previews" class="image-previews-container" style="display: none;">
                        <div class="image-previews" id="chat-image-previews-content"></div>
                    </div>

                    <div class="chat-input-row">
                        <textarea id="chat-input" class="chat-input" placeholder="输入消息..." onkeydown="handleChatKeydown(event)"></textarea>
                        <div class="chat-actions">
                            <button class="chat-btn" onclick="selectChatImage()" title="选择图片">📷</button>
                            <button class="chat-btn" onclick="pasteChatImage()" title="粘贴图片">📋</button>
                            <button class="chat-btn" onclick="clearChat()" title="清空聊天">🗑️</button>
                            <button class="chat-btn send-btn" onclick="sendChatMessage()" title="发送" id="send-chat-btn">🚀</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Socket.IO客户端 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>

    <!-- 主应用脚本 -->
    <script src="/app.js"></script>
</body>
</html>
