{"name": "mcp-feedback-collector", "version": "2.0.8", "description": "基于Node.js的MCP反馈收集器 - 支持AI工作汇报和用户反馈收集", "main": "dist/index.js", "bin": {"mcp-feedback-collector": "dist/cli.js"}, "type": "module", "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "feedback", "ai", "chat", "nodejs", "model-context-protocol", "claude", "anthropic"], "author": "MCP Feedback Collector Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sanshao85/mcp-feedback-collector-web.git"}, "homepage": "https://github.com/sanshao85/mcp-feedback-collector-web#readme", "bugs": {"url": "https://github.com/sanshao85/mcp-feedback-collector-web/issues"}, "scripts": {"build": "tsc && npm run copy-static", "copy-static": "node -e \"const fs=require('fs'); const path=require('path'); if(fs.existsSync('src/static')){fs.cpSync('src/static', 'dist/static', {recursive: true})}\"", "dev": "tsx watch --clear-screen=false src/cli.ts", "start": "node dist/cli.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/ --ext .ts", "lint:fix": "eslint src/ --ext .ts --fix", "clean": "node -e \"const fs=require('fs'); if(fs.existsSync('dist')){fs.rmSync('dist', {recursive: true, force: true})}\"", "prepare-release": "node scripts/prepare-release.cjs", "prepublishOnly": "npm run clean && npm run build"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "commander": "^11.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "find-free-port": "^2.0.0", "helmet": "^7.1.0", "node-fetch": "^3.3.2", "open": "^9.1.0", "sharp": "^0.32.6", "socket.io": "^4.7.2"}, "devDependencies": {"@types/compression": "^1.7.3", "@types/cors": "^2.8.14", "@types/express": "^4.17.17", "@types/jest": "^29.5.5", "@types/node": "^20.8.0", "@types/node-fetch": "^2.6.6", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^3.14.0", "typescript": "^5.2.2"}, "files": ["dist", "README.md", "LICENSE", "USER_GUIDE.md", "CONFIGURATION.md", "TROUBLESHOOTING.md", "CURSOR_CONFIGURATION.md", "ARCHITECTURE.md", "DOCUMENTATION_INDEX.md", "RELEASE_NOTES.md", "REMOTE_SERVER_CONFIGURATION.md"]}